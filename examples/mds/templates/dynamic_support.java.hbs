/**
 * @gencoder.generated: src/main/java/{{_replaceAll properties.dynamicSQLPkg '.' '/'}}/{{_pascalCase table.name}}DynamicSqlSupport.java
 */

package {{properties.dynamicSQLPkg}};

import java.sql.JDBCType;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.AliasableSqlTable;

public final class {{_pascalCase table.name}}DynamicSqlSupport {

    // @gencoder.block.start: DynamicSqlSupport
    public static final {{_pascalCase table.name}} {{_camelCase table.name}} = new {{_pascalCase table.name}}();

    {{#each table.columns}}
    public static final SqlColumn<{{> 'java_type.partial.hbs' columnType=type}}> {{_camelCase name}} = {{_camelCase ../table.name}}.{{_camelCase name}};
    {{/each}}

    public static final class {{_pascalCase table.name}} extends AliasableSqlTable<{{_pascalCase table.name}}> {

        {{#each table.columns}}
        public final SqlColumn<{{> 'java_type.partial.hbs' columnType=type}}> {{_camelCase name}} = column("`{{name}}`", {{> 'jdbc_type.partial.hbs' columnType=type}});
        {{/each}}

        public {{_pascalCase table.name}}() {
            super("{{table.name}}", {{_pascalCase table.name}}::new);
        }
    }
    // @gencoder.block.end: DynamicSqlSupport
}
