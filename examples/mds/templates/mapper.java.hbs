/**
 * @gencoder.generated: src/main/java/{{_replaceAll properties.mapperPkg '.' '/'}}/{{_pascalCase table.name}}Mapper.java
 */

package {{properties.mapperPkg}};

import static {{properties.dynamicSQLPkg}}.{{_pascalCase table.name}}DynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import {{properties.entityPkg}}.{{_pascalCase table.name}};
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface {{_pascalCase table.name}}Mapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {

    // @gencoder.block.start: mapper

    BasicColumn[] selectList = BasicColumn.columnList({{#each table.columns}}{{_camelCase name}}{{#unless @last}}, {{/unless}}{{/each}});

    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType={{> 'id_type.partial.hbs'}}.class)
    int insert(InsertStatementProvider<{{_pascalCase table.name}}> insertStatement);

    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="{{_pascalCase table.name}}Result", value = {
        {{#each table.columns}}
        @Result(column="{{name}}", property="{{_camelCase name}}", jdbcType={{> 'mybatis_type.partial.hbs' columnType=type}}{{#if isPrimaryKey}}, id=true{{/if}}){{#unless @last}},{{/unless}}
        {{/each}}
    })
    List<{{_pascalCase table.name}}> selectMany(SelectStatementProvider selectStatement);

    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("{{_pascalCase table.name}}Result")
    Optional<{{_pascalCase table.name}}> selectOne(SelectStatementProvider selectStatement);

    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, {{_camelCase table.name}}, completer);
    }

    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, {{_camelCase table.name}}, completer);
    }

    default int deleteByPrimaryKey({{> 'id_type.partial.hbs'}} id_) {
        return delete(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    default int insert({{_pascalCase table.name}} row) {
        return MyBatis3Utils.insert(this::insert, row, {{_camelCase table.name}}, c -> c
            {{#each table.columns}}
            {{#unless isPrimaryKey}}
            .map({{_camelCase name}}).toProperty("{{_camelCase name}}")
            {{/unless}}
            {{/each}}
        );
    }

    default int insertSelective({{_pascalCase table.name}} row) {
        return MyBatis3Utils.insert(this::insert, row, {{_camelCase table.name}}, c -> c
            {{#each table.columns}}
            {{#unless isPrimaryKey}}
            .map({{_camelCase name}}).toPropertyWhenPresent("{{_camelCase name}}", row::get{{_pascalCase name}})
            {{/unless}}
            {{/each}}
        );
    }

    default Optional<{{_pascalCase table.name}}> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, {{_camelCase table.name}}, completer);
    }

    default List<{{_pascalCase table.name}}> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, {{_camelCase table.name}}, completer);
    }

    default List<{{_pascalCase table.name}}> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, {{_camelCase table.name}}, completer);
    }

    default Optional<{{_pascalCase table.name}}> selectByPrimaryKey({{> 'id_type.partial.hbs'}} id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, {{_camelCase table.name}}, completer);
    }

    static UpdateDSL<UpdateModel> updateAllColumns({{_pascalCase table.name}} row, UpdateDSL<UpdateModel> dsl) {
        return dsl
        {{#each table.columns}}
            {{#unless isPrimaryKey}}
            .set({{_camelCase name}}).equalTo(row::get{{_pascalCase name}}){{#if @last}};{{/if}}
            {{/unless}}
        {{/each}}
    }

    static UpdateDSL<UpdateModel> updateSelectiveColumns({{_pascalCase table.name}} row, UpdateDSL<UpdateModel> dsl) {
        return dsl
        {{#each table.columns}}
            {{#unless isPrimaryKey}}
            .set({{_camelCase name}}).equalToWhenPresent(row::get{{_pascalCase name}}){{#if @last}};{{/if}}
            {{/unless}}
        {{/each}}
    }

    default int updateByPrimaryKey({{_pascalCase table.name}} row) {
        return update(c -> c
            {{#each table.columns}}
                {{#unless isPrimaryKey}}
                .set({{_camelCase name}}).equalTo(row::get{{_pascalCase name}})
                {{/unless}}
            {{/each}}
                .where(id, isEqualTo(row::getId))
        );
    }

    default int updateByPrimaryKeySelective({{_pascalCase table.name}} row) {
        return update(c -> c
            {{#each table.columns}}
                {{#unless isPrimaryKey}}
                .set({{_camelCase name}}).equalToWhenPresent(row::get{{_pascalCase name}})
                {{/unless}}
            {{/each}}
                .where(id, isEqualTo(row::getId))
        );
    }

    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    List<Map<String, Object>> generalSelect(SelectStatementProvider selectStatement);

    // @gencoder.block.end: mapper

}
