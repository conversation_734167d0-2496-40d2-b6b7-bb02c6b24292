/**
 * @gencoder.generated: src/main/java/{{_replaceAll properties.entityPkg '.' '/'}}/{{_pascalCase table.name}}.java
 */

package {{properties.entityPkg}};

/**
 * // @gencoder.block.start: table
 *
 * <p> table: {{table.name}}
 * <p> comment: {{table.comment}}
 * <p> indexes:
     {{#each table.indexes}}
 *   <p> {{name}}: ({{#each columns}}{{name}}{{#unless @last}}, {{/unless}}{{/each}})
     {{/each}}
 */
@lombok.Data
public final class {{_pascalCase table.name}} {

    {{#each table.columns}}
    /**
     * {{comment}}
     */
    private {{> 'java_type.partial.hbs' columnType=type}} {{_camelCase name}};
    {{/each}}

    // @gencoder.block.end: table

}
