{{~#if (_match 'varchar\(\d+\)|char|tinytext|text|mediumtext|longtext|json.*|enum.*' columnType)}}String
{{~else if (_match 'bigint.*' columnType)}}Long
{{~else if (_match 'int.*|integer.*|mediumint.*' columnType)}}Integer
{{~else if (_match 'smallint.*' columnType)}}Short
{{~else if (_match 'tinyint.*' columnType)}}Byte
{{~else if (_match 'bit.*|bool|boolean' columnType)}}Boolean
{{~else if (_match 'decimal.*' columnType)}}java.math.BigDecimal
{{~else if (_match 'float.*' columnType)}}Double
{{~else if (_match 'datetime' columnType)}}java.time.LocalDateTime
{{~else if (_match 'date' columnType)}}java.time.LocalDate
{{~else if (_match 'time' columnType)}}java.time.LocalTime
{{~else if (_match 'timestamp' columnType)}}java.time.LocalDateTime
{{~else if (_match 'varbinary' columnType)}}byte[]
{{~else}}Object
{{~/if}}
