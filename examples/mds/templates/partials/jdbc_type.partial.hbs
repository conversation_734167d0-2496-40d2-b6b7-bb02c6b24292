{{~#if (_match 'varchar\(\d+\)|char|tinytext|text|mediumtext|longtext|json.*|enum.*' columnType)}}JDBCType.VARCHAR
{{~else if (_match 'bigint.*' columnType)}}JDBCType.BIGINT
{{~else if (_match 'int.*|integer.*|mediumint.*' columnType)}}JDBCType.INTEGER
{{~else if (_match 'smallint.*' columnType)}}JDBCType.SMALLINT
{{~else if (_match 'tinyint.*' columnType)}}JDBCType.TINYINT
{{~else if (_match 'bit.*|bool|boolean' columnType)}}JDBCType.BOOLEAN
{{~else if (_match 'decimal.*' columnType)}}JDBCType.DECIMAL
{{~else if (_match 'float.*' columnType)}}JDBCType.FLOAT
{{~else if (_match 'datetime' columnType)}}JDBCType.TIMESTAMP
{{~else if (_match 'date' columnType)}}JDBCType.DATE
{{~else if (_match 'time' columnType)}}JDBCType.TIME
{{~else if (_match 'timestamp' columnType)}}JDBCType.TIMESTAMP
{{~else if (_match 'varbinary' columnType)}}JDBCType.VARBINARY
{{~else}}JDBCType.OTHER
{{~/if}}
