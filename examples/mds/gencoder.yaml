templatesDir: templates
blockMarker:
  start: '@gencoder.block.start:'
  end: '@gencoder.block.end:'
databases:
  - name: mysql
    dsn: 'mysql://root:root@localhost:3306/testdb'
    schema: testdb
    properties:
      entityPkg: com.example.entity.mysql
      mapperPkg: com.example.mapper.mysql
      dynamicSQLPkg: com.example.mapper.mysql
    tables:
      - name: 'user'
        ignoreColumns:
          - deleted_at
  - name: postgres
    dsn: 'postgres://root:root@localhost:5432/testdb?sslmode=disable'
    schema: public
    properties:
      entityPkg: com.example.entity.postgres
      mapperPkg: com.example.mapper.postgres
      dynamicSQLPkg: com.example.mapper.postgres
    tables:
      - name: 'user'
        ignoreColumns:
          - deleted_at
  - name: mssql
    dsn: 'mssql://sa:Sa123456..@localhost:1433/testdb?encrypt=disable'
    schema: dbo
    properties:
      entityPkg: com.example.entity.mssql
      mapperPkg: com.example.mapper.mssql
      dynamicSQLPkg: com.example.mapper.mssql
    tables:
      - name: 'user'
        ignoreColumns:
          - deleted_at
