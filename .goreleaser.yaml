version: 2

builds:
  - main: ./cmd/gencoder
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    goarch:
      - amd64
      - arm64

archives:
  - formats: [ 'tar.gz' ]
    files:
      - none* # https://goreleaser.com/customization/archive/#packaging-only-the-binaries
    # this name template makes the OS and Arch compatible with the results of `uname`.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        formats: [ 'zip' ]

# https://goreleaser.com/customization/release/#github
release:
  github:
    owner: DanielLiu1123
    name: gencoder
  draft: true
