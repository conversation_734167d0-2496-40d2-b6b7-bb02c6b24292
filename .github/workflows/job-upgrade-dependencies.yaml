name: Upgrade Dependencies

on:
  schedule:
    - cron: '0 11 * * *'

permissions:
  contents: write
  pull-requests: write

jobs:
  upgrade-dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: stable

      - name: Upgrade Dependencies
        continue-on-error: true
        run: |
          go get -u -v ./...
          go mod tidy

          git --no-pager diff go.mod
          diff_count=$(git --no-pager diff go.mod | wc -l | tr -d ' ')
          
          echo "diff_count=$diff_count" >> $GITHUB_ENV

          if [[ $diff_count -eq 0 ]]; then
            echo "No dependencies need to be upgraded"
          else
            echo "Dependencies changes detected, will create PR"
          fi

      - name: Create Pull Request
        if: env.diff_count != 0
        uses: peter-evans/create-pull-request@v7
        with:
          commit-message: "Upgrade dependencies"
          title: "Upgrade dependencies"
          body: ""
          branch: "upgrade-dependencies"
