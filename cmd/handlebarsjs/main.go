package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/DanielLiu1123/gencoder/pkg/util"
)

const (
	handlebarsJSURL    = "https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js"
	handlebarsJSOutput = "pkg/jsruntime/gen/handlebarsjs.gen.go"

	helperJSFile = "pkg/jsruntime/helper.js"
	helperOutput = "pkg/jsruntime/gen/helper.gen.go"
)

//go:generate sh -c "cd ../../ && go run cmd/handlebarsjs/main.go"
func main() {

	// Generate handlebarsjs.gen.go
	genHandlebarJS()

	// Generate helper.gen.go
	genHelper()

	log.Println("HandlebarsJS and HelperJS generated successfully!")
}

func genHelper() {
	jsCode, err := os.ReadFile(helperJSFile)
	if err != nil {
		log.Fatal("Error reading file:", err)
	}

	escapedJSCode := strings.ReplaceAll(string(jsCode), "`", "` + \"`\" + `")

	content := fmt.Sprintf("// Code generated by cmd/handlebarsjs/main.go; DO NOT EDIT.\n\npackage gen\n\nconst HelperJS = `%s`\n", escapedJSCode)

	generateFile(helperOutput, content)
}

func genHandlebarJS() {
	resp, err := http.Get(handlebarsJSURL)
	if err != nil {
		log.Fatal("Error fetching HandlebarsJS:", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Fatal("Error closing response body:", err)
		}
	}(resp.Body)

	jsCode, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatal("Error reading response:", err)
	}

	escapedJSCode := strings.ReplaceAll(string(jsCode), "`", "` + \"`\" + `")

	content := fmt.Sprintf("// Code generated by cmd/handlebarsjs/main.go; DO NOT EDIT.\n\npackage gen\n\nconst HandlebarsJS = `%s`\n", escapedJSCode)

	generateFile(handlebarsJSOutput, content)
}

func generateFile(filePath, content string) {
	err := util.WriteFile(filePath, []byte(content))
	if err != nil {
		log.Fatal("Error writing file:", err)
	}
}
