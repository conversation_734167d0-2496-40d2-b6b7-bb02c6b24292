package main

import (
	"log"

	"github.com/DanielLiu1123/gencoder/pkg/model"
	"github.com/DanielLiu1123/gencoder/pkg/util"
	"github.com/invopop/jsonschema"
)

const jsonschemaFile = "schema.json"

//go:generate sh -c "cd ../../ && go run cmd/jsonschema/main.go"
func main() {
	schema := jsonschema.Reflect(&model.Config{})
	schema.ID = "https://github.com/DanielLiu1123/gencoder/blob/main/pkg/model/config.go"

	err := util.WriteFile(jsonschemaFile, []byte(util.ToJson(schema)))
	if err != nil {
		log.Fatal(err)
	}

	log.Println("JSON schema generated at", jsonschemaFile)
}
